import streamlit as st
import openai
import json
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
from datetime import datetime
import base64
from io import BytesIO
import time

# Set page config
st.set_page_config(
    page_title="NPD Market Research Tool",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.8rem;
        font-weight: bold;
        color: #2c3e50;
        margin-top: 2rem;
        margin-bottom: 1rem;
        border-bottom: 3px solid #3498db;
        padding-bottom: 0.5rem;
    }
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
        text-align: center;
    }
    .insight-box {
        background: #f8f9fa;
        border-left: 4px solid #17a2b8;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 5px;
    }
    .gap-highlight {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 5px;
        padding: 1rem;
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)

class NPDAnalyzer:
    def __init__(self, api_key):
        self.client = openai.OpenAI(api_key=api_key)
    
    def analyze_keywords(self, product_category):
        """Analyze keywords and market trends"""
        prompt = f"""
        As an expert market researcher and SEO specialist, analyze the product category "{product_category}" and provide comprehensive keyword analysis data.
        
        Provide a detailed JSON response with the following structure:
        {{
            "primary_keywords": [
                {{
                    "keyword": "keyword phrase",
                    "search_volume": estimated_monthly_searches,
                    "competition_score": 1-100,
                    "trend_direction": "up/down/stable",
                    "commercial_intent": "high/medium/low"
                }}
            ],
            "long_tail_keywords": [
                {{
                    "keyword": "long tail phrase",
                    "search_volume": estimated_monthly_searches,
                    "competition_score": 1-100,
                    "opportunity_score": 1-100
                }}
            ],
            "seasonal_trends": [
                {{
                    "month": "month_name",
                    "relative_interest": 1-100
                }}
            ],
            "related_categories": ["category1", "category2", "category3"],
            "market_size_estimate": "market size description",
            "growth_rate": "percentage growth",
            "key_insights": ["insight1", "insight2", "insight3"]
        }}
        
        Base your analysis on realistic market data patterns and industry knowledge. Be specific with numbers and actionable insights.
        """
        
        response = self.client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3
        )
        
        return json.loads(response.choices[0].message.content)
    
    def analyze_dissonances(self, product_category):
        """Analyze market gaps and customer pain points"""
        prompt = f"""
        As an expert market analyst specializing in customer sentiment and product gaps, analyze the "{product_category}" market for dissonances and opportunities.
        
        Provide a detailed JSON response with the following structure:
        {{
            "common_complaints": [
                {{
                    "complaint": "specific complaint",
                    "frequency_score": 1-100,
                    "severity": "high/medium/low",
                    "category": "pricing/quality/functionality/service/other"
                }}
            ],
            "market_gaps": [
                {{
                    "gap_description": "detailed gap description",
                    "market_opportunity": "high/medium/low",
                    "target_demographic": "demographic description",
                    "potential_solution": "solution suggestion"
                }}
            ],
            "sentiment_analysis": {{
                "overall_sentiment": "positive/negative/neutral",
                "satisfaction_score": 1-100,
                "recommendation_rate": "percentage",
                "key_pain_points": ["pain1", "pain2", "pain3"]
            }},
            "competitor_weaknesses": [
                {{
                    "weakness": "specific weakness",
                    "affected_brands": ["brand1", "brand2"],
                    "opportunity_rating": 1-100
                }}
            ],
            "unmet_needs": [
                {{
                    "need": "specific unmet need",
                    "demand_level": "high/medium/low",
                    "feasibility": "high/medium/low",
                    "innovation_potential": 1-100
                }}
            ],
            "recommendations": ["recommendation1", "recommendation2", "recommendation3"]
        }}
        
        Base your analysis on realistic customer feedback patterns and market research principles.
        """
        
        response = self.client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3
        )
        
        return json.loads(response.choices[0].message.content)
    
    def generate_html_report(self, product_category, keyword_data, dissonance_data):
        """Generate comprehensive HTML report"""
        current_date = datetime.now().strftime("%B %d, %Y")
        
        # Create visualizations
        keyword_fig = self.create_keyword_visualization(keyword_data)
        sentiment_fig = self.create_sentiment_visualization(dissonance_data)
        opportunity_fig = self.create_opportunity_visualization(dissonance_data)
        seasonal_fig = self.create_seasonal_visualization(keyword_data)
        
        # Convert plots to HTML
        keyword_html = keyword_fig.to_html(full_html=False, include_plotlyjs='cdn')
        sentiment_html = sentiment_fig.to_html(full_html=False, include_plotlyjs='cdn')
        opportunity_html = opportunity_fig.to_html(full_html=False, include_plotlyjs='cdn')
        seasonal_html = seasonal_fig.to_html(full_html=False, include_plotlyjs='cdn')
        
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>NPD Market Research Report - {product_category.title()}</title>
            <style>
                body {{
                    font-family: 'Arial', sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                    background: #f8f9fa;
                }}
                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 2rem;
                    border-radius: 10px;
                    text-align: center;
                    margin-bottom: 2rem;
                }}
                .section {{
                    background: white;
                    padding: 2rem;
                    margin: 2rem 0;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                }}
                .section h2 {{
                    color: #2c3e50;
                    border-bottom: 3px solid #3498db;
                    padding-bottom: 0.5rem;
                    margin-bottom: 1.5rem;
                }}
                .metric-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1rem;
                    margin: 1rem 0;
                }}
                .metric-card {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem;
                    border-radius: 10px;
                    text-align: center;
                }}
                .metric-value {{
                    font-size: 2rem;
                    font-weight: bold;
                    margin-bottom: 0.5rem;
                }}
                .metric-label {{
                    font-size: 0.9rem;
                    opacity: 0.9;
                }}
                .insight-box {{
                    background: #e8f4f8;
                    border-left: 4px solid #17a2b8;
                    padding: 1rem;
                    margin: 1rem 0;
                    border-radius: 5px;
                }}
                .gap-highlight {{
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 5px;
                    padding: 1rem;
                    margin: 0.5rem 0;
                }}
                .recommendation {{
                    background: #d4edda;
                    border: 1px solid #c3e6cb;
                    color: #155724;
                    padding: 1rem;
                    border-radius: 5px;
                    margin: 0.5rem 0;
                }}
                .table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 1rem 0;
                }}
                .table th, .table td {{
                    border: 1px solid #ddd;
                    padding: 12px;
                    text-align: left;
                }}
                .table th {{
                    background: #f8f9fa;
                    font-weight: bold;
                }}
                .chart-container {{
                    margin: 2rem 0;
                    padding: 1rem;
                    background: #f8f9fa;
                    border-radius: 5px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>NPD Market Research Report</h1>
                <h2>{product_category.title()} Category Analysis</h2>
                <p>Generated on {current_date}</p>
            </div>
            
            <div class="section">
                <h2>📊 Executive Summary</h2>
                <div class="metric-grid">
                    <div class="metric-card">
                        <div class="metric-value">{keyword_data['market_size_estimate']}</div>
                        <div class="metric-label">Market Size</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{keyword_data['growth_rate']}</div>
                        <div class="metric-label">Growth Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{dissonance_data['sentiment_analysis']['satisfaction_score']}/100</div>
                        <div class="metric-label">Customer Satisfaction</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{len(dissonance_data['market_gaps'])}</div>
                        <div class="metric-label">Market Gaps Identified</div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🔍 Keyword Analysis</h2>
                <div class="chart-container">
                    {keyword_html}
                </div>
                
                <h3>Primary Keywords Performance</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Keyword</th>
                            <th>Search Volume</th>
                            <th>Competition</th>
                            <th>Trend</th>
                            <th>Commercial Intent</th>
                        </tr>
                    </thead>
                    <tbody>
                        {''.join([f"""
                        <tr>
                            <td>{kw['keyword']}</td>
                            <td>{kw['search_volume']:,}</td>
                            <td>{kw['competition_score']}/100</td>
                            <td>{kw['trend_direction']}</td>
                            <td>{kw['commercial_intent']}</td>
                        </tr>
                        """ for kw in keyword_data['primary_keywords']])}
                    </tbody>
                </table>
                
                <div class="insight-box">
                    <h4>Key Insights:</h4>
                    <ul>
                        {''.join([f"<li>{insight}</li>" for insight in keyword_data['key_insights']])}
                    </ul>
                </div>
            </div>
            
            <div class="section">
                <h2>📈 Seasonal Trends</h2>
                <div class="chart-container">
                    {seasonal_html}
                </div>
            </div>
            
            <div class="section">
                <h2>🎯 Market Dissonances & Gaps</h2>
                <div class="chart-container">
                    {sentiment_html}
                </div>
                
                <h3>Top Customer Complaints</h3>
                {''.join([f"""
                <div class="gap-highlight">
                    <strong>{complaint['complaint']}</strong><br>
                    Frequency: {complaint['frequency_score']}/100 | Severity: {complaint['severity'].title()} | Category: {complaint['category'].title()}
                </div>
                """ for complaint in dissonance_data['common_complaints'][:5]])}
                
                <h3>Market Opportunities</h3>
                <div class="chart-container">
                    {opportunity_html}
                </div>
                
                {''.join([f"""
                <div class="gap-highlight">
                    <strong>Gap:</strong> {gap['gap_description']}<br>
                    <strong>Opportunity:</strong> {gap['market_opportunity'].title()}<br>
                    <strong>Target:</strong> {gap['target_demographic']}<br>
                    <strong>Potential Solution:</strong> {gap['potential_solution']}
                </div>
                """ for gap in dissonance_data['market_gaps']])}
            </div>
            
            <div class="section">
                <h2>💡 Strategic Recommendations</h2>
                {''.join([f"""
                <div class="recommendation">
                    <strong>Recommendation {i+1}:</strong> {rec}
                </div>
                """ for i, rec in enumerate(dissonance_data['recommendations'])])}
            </div>
            
            <div class="section">
                <h2>🔧 Unmet Customer Needs</h2>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Need</th>
                            <th>Demand Level</th>
                            <th>Feasibility</th>
                            <th>Innovation Potential</th>
                        </tr>
                    </thead>
                    <tbody>
                        {''.join([f"""
                        <tr>
                            <td>{need['need']}</td>
                            <td>{need['demand_level'].title()}</td>
                            <td>{need['feasibility'].title()}</td>
                            <td>{need['innovation_potential']}/100</td>
                        </tr>
                        """ for need in dissonance_data['unmet_needs']])}
                    </tbody>
                </table>
            </div>
            
            <div class="section">
                <h2>🏆 Competitor Analysis</h2>
                <h3>Identified Weaknesses</h3>
                {''.join([f"""
                <div class="insight-box">
                    <strong>Weakness:</strong> {weakness['weakness']}<br>
                    <strong>Affected Brands:</strong> {', '.join(weakness['affected_brands'])}<br>
                    <strong>Opportunity Rating:</strong> {weakness['opportunity_rating']}/100
                </div>
                """ for weakness in dissonance_data['competitor_weaknesses']])}
            </div>
            
            <div class="section">
                <h2>📋 Methodology</h2>
                <p>This report was generated using advanced AI analysis combining:</p>
                <ul>
                    <li>Keyword trend analysis and search volume estimation</li>
                    <li>Sentiment analysis of customer feedback and reviews</li>
                    <li>Market gap identification through competitor analysis</li>
                    <li>Opportunity scoring based on demand and feasibility metrics</li>
                </ul>
                <p><em>Generated by NPD Market Research Tool - {current_date}</em></p>
            </div>
        </body>
        </html>
        """
        
        return html_content
    
    def create_keyword_visualization(self, keyword_data):
        """Create keyword analysis visualization"""
        keywords = [kw['keyword'] for kw in keyword_data['primary_keywords']]
        volumes = [kw['search_volume'] for kw in keyword_data['primary_keywords']]
        competition = [kw['competition_score'] for kw in keyword_data['primary_keywords']]
        
        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=('Search Volume vs Competition', 'Keyword Performance'),
            specs=[[{"secondary_y": False}, {"type": "bar"}]]
        )
        
        # Scatter plot
        fig.add_trace(
            go.Scatter(
                x=competition, y=volumes, 
                mode='markers+text',
                text=keywords,
                textposition="top center",
                marker=dict(size=10, color='blue'),
                name='Keywords'
            ),
            row=1, col=1
        )
        
        # Bar chart
        fig.add_trace(
            go.Bar(x=keywords, y=volumes, name='Search Volume', marker_color='lightblue'),
            row=1, col=2
        )
        
        fig.update_layout(
            title='Keyword Analysis Overview',
            showlegend=False,
            height=400
        )
        
        return fig
    
    def create_sentiment_visualization(self, dissonance_data):
        """Create sentiment analysis visualization"""
        complaints = dissonance_data['common_complaints'][:5]
        categories = [c['category'] for c in complaints]
        frequencies = [c['frequency_score'] for c in complaints]
        
        fig = go.Figure(data=[
            go.Bar(x=categories, y=frequencies, marker_color='salmon')
        ])
        
        fig.update_layout(
            title='Top Customer Complaint Categories',
            xaxis_title='Category',
            yaxis_title='Frequency Score',
            height=400
        )
        
        return fig
    
    def create_opportunity_visualization(self, dissonance_data):
        """Create opportunity analysis visualization"""
        gaps = dissonance_data['market_gaps']
        gap_names = [f"Gap {i+1}" for i in range(len(gaps))]
        opportunity_levels = [1 if gap['market_opportunity'] == 'low' else 2 if gap['market_opportunity'] == 'medium' else 3 for gap in gaps]
        
        fig = go.Figure(data=[
            go.Bar(x=gap_names, y=opportunity_levels, 
                   text=[gap['gap_description'][:50] + "..." for gap in gaps],
                   textposition='auto',
                   marker_color='lightgreen')
        ])
        
        fig.update_layout(
            title='Market Opportunity Analysis',
            xaxis_title='Identified Gaps',
            yaxis_title='Opportunity Level (1-3)',
            height=400
        )
        
        return fig
    
    def create_seasonal_visualization(self, keyword_data):
        """Create seasonal trends visualization"""
        seasonal_data = keyword_data['seasonal_trends']
        months = [s['month'] for s in seasonal_data]
        interests = [s['relative_interest'] for s in seasonal_data]
        
        fig = go.Figure(data=[
            go.Scatter(x=months, y=interests, mode='lines+markers', 
                      line=dict(color='blue', width=3),
                      marker=dict(size=8))
        ])
        
        fig.update_layout(
            title='Seasonal Interest Trends',
            xaxis_title='Month',
            yaxis_title='Relative Interest (1-100)',
            height=400
        )
        
        return fig

def main():
    st.markdown('<div class="main-header">🚀 NPD Market Research Tool</div>', unsafe_allow_html=True)
    st.markdown("**Comprehensive market analysis for new product development**")
    
    # Sidebar for API key
    with st.sidebar:
        st.header("🔑 Configuration")
        api_key = st.text_input("OpenAI API Key", type="password", help="Enter your OpenAI API key")
        
        if api_key:
            st.success("API Key configured!")
        else:
            st.warning("Please enter your OpenAI API key to continue")
    
    # Main interface
    if api_key:
        analyzer = NPDAnalyzer(api_key)
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            product_category = st.text_input(
                "🏷️ Product Category", 
                placeholder="e.g., sunscreen, wireless headphones, protein powder",
                help="Enter the product category you want to analyze"
            )
        
        with col2:
            st.markdown("<br>", unsafe_allow_html=True)
            analyze_button = st.button("🔍 Start Analysis", type="primary", use_container_width=True)
        
        if analyze_button and product_category:
            # Progress tracking
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            try:
                # Step 1: Keyword Analysis
                status_text.text("🔍 Analyzing keywords and market trends...")
                progress_bar.progress(25)
                
                keyword_data = analyzer.analyze_keywords(product_category)
                
                # Step 2: Dissonance Analysis
                status_text.text("🎯 Analyzing market gaps and customer sentiment...")
                progress_bar.progress(50)
                
                dissonance_data = analyzer.analyze_dissonances(product_category)
                
                # Step 3: Generate Report
                status_text.text("📊 Generating comprehensive report...")
                progress_bar.progress(75)
                
                html_report = analyzer.generate_html_report(product_category, keyword_data, dissonance_data)
                
                # Step 4: Display Results
                status_text.text("✅ Analysis complete!")
                progress_bar.progress(100)
                
                # Display key metrics
                st.markdown('<div class="section-header">📈 Key Metrics</div>', unsafe_allow_html=True)
                
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.markdown(f'''
                    <div class="metric-card">
                        <div style="font-size: 1.5rem; font-weight: bold;">{len(keyword_data["primary_keywords"])}</div>
                        <div>Primary Keywords</div>
                    </div>
                    ''', unsafe_allow_html=True)
                
                with col2:
                    st.markdown(f'''
                    <div class="metric-card">
                        <div style="font-size: 1.5rem; font-weight: bold;">{dissonance_data["sentiment_analysis"]["satisfaction_score"]}/100</div>
                        <div>Satisfaction Score</div>
                    </div>
                    ''', unsafe_allow_html=True)
                
                with col3:
                    st.markdown(f'''
                    <div class="metric-card">
                        <div style="font-size: 1.5rem; font-weight: bold;">{len(dissonance_data["market_gaps"])}</div>
                        <div>Market Gaps</div>
                    </div>
                    ''', unsafe_allow_html=True)
                
                with col4:
                    st.markdown(f'''
                    <div class="metric-card">
                        <div style="font-size: 1.5rem; font-weight: bold;">{len(dissonance_data["unmet_needs"])}</div>
                        <div>Unmet Needs</div>
                    </div>
                    ''', unsafe_allow_html=True)
                
                # Display visualizations
                st.markdown('<div class="section-header">📊 Analysis Visualizations</div>', unsafe_allow_html=True)
                
                col1, col2 = st.columns(2)
                
                with col1:
                    keyword_fig = analyzer.create_keyword_visualization(keyword_data)
                    st.plotly_chart(keyword_fig, use_container_width=True)
                
                with col2:
                    sentiment_fig = analyzer.create_sentiment_visualization(dissonance_data)
                    st.plotly_chart(sentiment_fig, use_container_width=True)
                
                # Show top insights
                st.markdown('<div class="section-header">💡 Key Insights</div>', unsafe_allow_html=True)
                
                for insight in keyword_data['key_insights']:
                    st.markdown(f'''
                    <div class="insight-box">
                        <strong>🔍 Market Insight:</strong> {insight}
                    </div>
                    ''', unsafe_allow_html=True)
                
                # Show market gaps
                st.markdown('<div class="section-header">🎯 Market Opportunities</div>', unsafe_allow_html=True)
                
                for gap in dissonance_data['market_gaps'][:3]:
                    st.markdown(f'''
                    <div class="gap-highlight">
                        <strong>Gap:</strong> {gap['gap_description']}<br>
                        <strong>Opportunity Level:</strong> {gap['market_opportunity'].title()}<br>
                        <strong>Target:</strong> {gap['target_demographic']}
                    </div>
                    ''', unsafe_allow_html=True)
                
                # Download report
                st.markdown('<div class="section-header">📄 Download Report</div>', unsafe_allow_html=True)
                
                # Create download button
                b64_html = base64.b64encode(html_report.encode()).decode()
                href = f'<a href="data:text/html;base64,{b64_html}" download="NPD_Report_{product_category}_{datetime.now().strftime("%Y%m%d")}.html">📥 Download Full HTML Report</a>'
                st.markdown(href, unsafe_allow_html=True)
                
                # Clear progress indicators
                time.sleep(1)
                progress_bar.empty()
                status_text.empty()
                
            except Exception as e:
                st.error(f"An error occurred during analysis: {str(e)}")
                st.info("Please check your API key and try again.")
    
    else:
        st.info("👈 Please enter your OpenAI API key in the sidebar to get started.")
        
        # Show example of what the tool can do
        st.markdown("## 🎯 What This Tool Provides:")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("""
            **🔍 Keyword Analysis:**
            - Search volume estimates
            - Competition analysis
            - Trend identification
            - Commercial intent scoring
            - Seasonal patterns
            """)
        
        with col2:
            st.markdown("""
            **🎯 Market Gap Analysis:**
            - Customer pain points
            - Competitor weaknesses
            - Unmet needs identification
            - Opportunity scoring
            - Strategic recommendations
            """)
        
        st.markdown("## 📊 Sample Output Features:")
        st.markdown("""
        - **Interactive Visualizations**: Charts and graphs showing market trends
        - **Comprehensive HTML Reports**: Professional reports with statistical content
        - **Opportunity Scoring**: Quantified market opportunities
        - **Actionable Insights**: Data-driven recommendations for product development
        """)

if __name__ == "__main__":
    main()